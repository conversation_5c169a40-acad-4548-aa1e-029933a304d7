package hk.org.ha.sc3.sybasechatops.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "tapdata")
public class TapdataConfig {
    
    private String baseUrl;
    private String username;
    private String password;
    private int waitTimeoutSecond = 30;
    private int screenshotDelayMs = 3000;
    private int windowWidth = 1920;
    private int windowHeight = 1080;
}
