package hk.org.ha.sc3.sybasechatops.component.arghandler.tapdata;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import javax.servlet.http.HttpSession;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import hk.org.ha.sc3.sybasechatops.constant.cmd.CmdArgEnum;
import hk.org.ha.sc3.sybasechatops.interfaces.ICommandArgHandler;
import hk.org.ha.sc3.sybasechatops.model.Option;
import hk.org.ha.sc3.sybasechatops.model.db.CommandArgument;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class TapdataPageArgHandler extends ICommandArgHandler {

    @Override
    public CmdArgEnum getCmdArgType() {
        return CmdArgEnum.ARG_TAPDATA_PAGE;
    }

    @Override
    public List<Option> getOptions(CommandArgument argument, HttpSession httpSession, String msgChain) {
        List<Option> options = new ArrayList<>();
        ObjectMapper mapper = new ObjectMapper();
        HashMap<String, String> hiddenValue = new HashMap<>();

        // Available Tapdata pages
        String[] pages = {"dashboard", "migrate", "dataflow"};
        String[] pageDescriptions = {
            "Dashboard - Main overview page",
            "Migrate - Data migration tasks",
            "Dataflow - Data flow management"
        };

        for (int i = 0; i < pages.length; i++) {
            hiddenValue.clear();
            hiddenValue.put(getCmdArgType().name(), pages[i]);
            
            try {
                String jsonStr = mapper.writeValueAsString(hiddenValue);
                Option option = Option.builder()
                    .text(pageDescriptions[i])  // Text displayed to the user
                    .value(jsonStr)             // Value to be appended to msgChain when selected
                    .nextLabel(msgChain + "|" + getCmdArgType())
                    .build();
                options.add(option);
            } catch (JsonProcessingException e) {
                log.error("Error processing JSON for Tapdata page: {}", pages[i], e);
            }
        }

        return options;
    }
}
