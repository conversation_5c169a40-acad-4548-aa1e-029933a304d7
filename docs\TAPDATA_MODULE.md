# Tapdata Dashboard Capture Module

## Overview

The Tapdata module provides automated web dashboard capture functionality for the Tapdata management interface. It uses Selenium WebDriver to automate browser interactions, login to the Tapdata web interface, and capture screenshots of various dashboard pages.

## Features

- **Automated Login**: Automatically logs into the Tapdata web interface using configured credentials
- **Dashboard Capture**: Captures screenshots of the main dashboard
- **Specific Page Capture**: Captures screenshots of specific pages within the Tapdata interface
- **Configurable Settings**: Supports customizable timeouts, window sizes, and screenshot delays

## Configuration

Add the following configuration to your `application.yml`:

```yaml
tapdata:
  baseUrl: 'http://tapcdcvmctst71a:3030'
  username: ${tapdata_username}
  password: ${tapdata_password}
  waitTimeoutSecond: 30
  screenshotDelayMs: 3000
  windowWidth: 1920
  windowHeight: 1080
```

### Configuration Parameters

- `baseUrl`: The base URL of the Tapdata management interface
- `username`: Username for authentication (use environment variable)
- `password`: Password for authentication (use environment variable)
- `waitTimeoutSecond`: Maximum time to wait for web elements (default: 30 seconds)
- `screenshotDelayMs`: Delay before taking screenshot to ensure page is fully loaded (default: 3000ms)
- `windowWidth`: Browser window width for screenshots (default: 1920px)
- `windowHeight`: Browser window height for screenshots (default: 1080px)

## Usage

### Available Commands

1. **capturePage**: Captures a Tapdata page after login with user-selectable page argument

### Available Pages

- **dashboard**: Main overview page
- **migrate**: Data migration tasks
- **dataflow**: Data flow management

### Menu Navigation

The module integrates with the chatops menu system using argument handlers:

```
@itbot|v2|{session}|TAPDATA
└── capturePage
    └── Select page to capture:
        ├── Dashboard - Main overview page
        ├── Migrate - Data migration tasks
        └── Dataflow - Data flow management
```

### API Endpoints

The Tapdata module integrates with the existing ChatOps infrastructure:

- `POST /v2/menu` - Main menu controller (BaseMenuControllerV2)
- `POST /v2/health_check` - Health check and command execution (BaseHealthCheckControllerV2)

## Implementation Details

### Key Components

1. **TapdataService**: Main service class implementing the ICommand interface
2. **TapdataConfig**: Configuration properties class
3. **TapdataPageArgHandler**: ICommandArgHandler for page selection
4. **BaseMenuControllerV2**: Generic menu controller that handles Tapdata through DatabaseTypeEnum
5. **BaseHealthCheckControllerV2**: Generic health check controller that executes Tapdata commands

### Login Process

The module automatically handles the login process:

1. Navigates to the Tapdata login page
2. Locates email and password input fields using XPath selectors
3. Enters credentials and clicks the Sign in button
4. Waits for login completion before proceeding

### Screenshot Process

1. Sets browser window to configured dimensions
2. Waits for the specified delay to ensure page loading
3. Captures full page screenshot
4. Saves screenshot with timestamp in the configured directory

## Web Element Selectors

The module uses the following selectors for login:

- Email input: `//input[@placeholder='Enter your email']`
- Password input: `//input[@placeholder='Enter your password']`
- Sign in button: `//button[contains(., 'Sign in')]`

## Error Handling

- Handles login failures gracefully
- Provides detailed error logging
- Returns appropriate error codes and messages
- Ensures WebDriver cleanup in finally blocks

## Database Setup

Before using the Tapdata module, run the migration script to set up the required database entries:

```sql
-- Run the migration script
source mybatis/db/migration/scripts/20250903001_ADD_TAPDATA_SUPPORT.sql
```

This script will:
- Add Tapdata commands to the `chatops_command` table
- Create command groups in the `chatops_command_group` table
- Set up command arguments and argument mappings for page selection
- Set up command group mappings
- Add TAPDATA to the global allowed database types
- Add a sample Tapdata instance

## Dependencies

- Selenium WebDriver (Firefox)
- Spring Boot
- WebDriverService (shared service for browser automation)
- MySQL database with ChatOps schema

## Testing

Unit tests are provided in `TapdataServiceTest.java` covering:

- Command type verification
- Command routing logic
- Configuration validation
- Error handling scenarios

## Security Considerations

- Credentials should be stored as environment variables
- Use encrypted configuration for sensitive data
- Ensure proper session cleanup after operations
- Consider implementing rate limiting for automated requests

## Troubleshooting

### Common Issues

1. **Login Failures**: Verify credentials and network connectivity
2. **Element Not Found**: Check if Tapdata UI has changed and update selectors
3. **Timeout Errors**: Increase `waitTimeoutSecond` for slow networks
4. **Screenshot Issues**: Verify WebDriver configuration and permissions

### Debugging

Enable debug logging for detailed execution information:

```yaml
logging:
  level:
    hk.org.ha.sc3.sybasechatops.service.TapdataService: DEBUG
```

## Future Enhancements

- Support for multiple authentication methods
- Dynamic element detection
- Page-specific screenshot optimization
- Integration with monitoring systems
- Support for different browsers
