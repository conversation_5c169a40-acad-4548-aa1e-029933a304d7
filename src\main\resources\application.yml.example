spring:
  application:
    name: chatops
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ${mysql_jdbc_url}
    username: ${mysql_username}
    password: ${mysql_password}
    hikari:
      schema: health_check
  jpa:
    hibernate:
      ddl-auto: none
    open-in-view: false
  show-sql: false

server:
  servlet:
    context-path: /chatops/menu/
  version: 2.2.0
  port: '8080'

## custome properties
pdf:
  storage: ./pdf

grafana:
  baseUrl: ${grafana_server_url}
  username: ${grafana_username}
  password: ${grafana_password}
  screenshot-delay-ms: 3000
  wait-timeout-second: 3
  server-domain: serverdev.hadev.org.hk

cyberark:
  baseUrl: ${cyberark_api_address}
  request-timeout: 5
  user: ${cyberark_username}
  password: ${cyberark_password}

cloudera:
  screenshot-delay-ms: 3000

oemagent:
  baseUrl: 'https://eap-oem-sit.serverdev.hadev.org.hk:7799/'
  username: ${oem_user}
  password: ${oem_password}
  suffix: '.serverdev.hadev.org.hk:1833'
  shMasterDirectory: ${python_script_path}
  ymlPath: ${application_yml_path}
  scriptHost: ${script_host}
  scriptHostUser: oracle

notification:
  email: ${manager_email}

webdriver:
  location: /dbms/oratmp/chatops/sit/driver/geckodriver
  headless: true
  binary-path: /dbms/oratmp/firefox/firefox

ansible:
  baseUrl: 'https://autaapvmctst11a.serverdev.hadev.org.hk/api/'
  user: ${user}
  password: ${password}
  interval: 5

webhook:
  env: dev
  url: https://aiops-chatops-webhook-${webhook.env}.server.ha.org.hk/webhook
  token: ${webhook_token}

gtm:
  url: http://localhost

tapdata:
  baseUrl: 'http://tapcdcvmctst71a:3030'
  username: ${tapdata_username}
  password: ${tapdata_password}
  waitTimeoutSecond: 30
  screenshotDelayMs: 3000
  windowWidth: 1920
  windowHeight: 1080

folderdistributor:
  jarPath: /dbms/oratmp/chatops/dev/chatops-spring-${server.version}.jar
  oemDestinationFolderPath: /dbms/oratmp/chatops/dev/python_oem
  startDestinationFolderPath: /dbms/oratmp/chatops

## logging
logging:
  file:
    name: /dbms/oratmp/chatops/dev/logs/sc3-chatops.log
  logback:
    rollingpolicy:
      file-name-pattern: ${LOG_FILE}.%d{yyyy-MM-dd}.%i.gz
      max-file-size: 100MB
      max-history: 21
      total-size-cap: 10GB
  level:
    hk:
      org:
        ha:
          sc3:
            sybasechatops:
              service: DEBUG
    org:
      apache:
        sshd: ERROR
      springframework:
        web:
          filter:
            CommonsRequestLoggingFilter: DEBUG

## Jasypt encryption configuration
jasypt:
  encryptor:
    password: ${JASYPT_ENCRYPTOR_PASSWORD:defaultkey}

chatops:
  security:
    token:
      enable-migration: true