package hk.org.ha.sc3.sybasechatops.service;

import org.openqa.selenium.By;
import org.openqa.selenium.Dimension;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.firefox.FirefoxDriver;
import org.openqa.selenium.support.ui.FluentWait;
import org.openqa.selenium.support.ui.Wait;
import org.springframework.stereotype.Service;

import java.time.Duration;

import hk.org.ha.sc3.sybasechatops.config.TapdataConfig;
import hk.org.ha.sc3.sybasechatops.constant.CmdTypeEnum;
import hk.org.ha.sc3.sybasechatops.interfaces.ICommand;
import hk.org.ha.sc3.sybasechatops.model.db.Command;
import hk.org.ha.sc3.sybasechatops.model.db.CommandResult;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class TapdataService implements ICommand {

    private WebDriverService webDriverService;
    private TapdataConfig tapdataConfig;

    public TapdataService(WebDriverService webDriverService, TapdataConfig tapdataConfig) {
        this.webDriverService = webDriverService;
        this.tapdataConfig = tapdataConfig;
    }

    private WebElement waitForElement(By locator, FirefoxDriver driver) {
        Wait<WebDriver> wait = new FluentWait<WebDriver>(driver)
                .withTimeout(Duration.ofSeconds(tapdataConfig.getWaitTimeoutSecond()))
                .pollingEvery(Duration.ofSeconds(1))
                .ignoring(NoSuchElementException.class);

        return wait.until(d -> d.findElement(locator));
    }

    private void login(FirefoxDriver driver) throws InterruptedException {
        log.info("Starting Tapdata login process");
        
        // Wait for email input field and enter username
        WebElement emailInput = waitForElement(By.xpath("//input[@placeholder='Enter your email']"), driver);
        emailInput.clear();
        emailInput.sendKeys(tapdataConfig.getUsername());
        log.info("Entered username");

        // Wait for password input field and enter password
        WebElement passwordInput = waitForElement(By.xpath("//input[@placeholder='Enter your password']"), driver);
        passwordInput.clear();
        passwordInput.sendKeys(tapdataConfig.getPassword());
        log.info("Entered password");

        // Find and click the Sign in button
        WebElement signInButton = waitForElement(By.xpath("//button[contains(., 'Sign in')]"), driver);
        signInButton.click();
        log.info("Clicked Sign in button");

        // Wait a bit for the login to process
        Thread.sleep(3000);
    }

    private void setWindowSize(FirefoxDriver driver) {
        driver.manage().window()
                .setSize(new Dimension(tapdataConfig.getWindowWidth(), tapdataConfig.getWindowHeight()));
    }

    private CommandResult executeTapdataCommand(String path) throws Exception {
        FirefoxDriver driver = null;
        long startTime = System.currentTimeMillis();
        long endTime;
        String file = "";
        String currentUrl = "";
        double elapsedTime = 0;
        
        try {
            driver = this.webDriverService.firefoxDriver();
            
            // Navigate to the Tapdata login page
            String loginUrl = tapdataConfig.getBaseUrl();
            if (!loginUrl.endsWith("/")) {
                loginUrl += "/";
            }
            
            driver.navigate().to(loginUrl);
            currentUrl = driver.getCurrentUrl();
            log.info("Navigated to Tapdata URL: {}", currentUrl);

            // Perform login
            this.login(driver);

            // Navigate to specific path if provided
            if (path != null && !path.isEmpty()) {
                String targetUrl = loginUrl + path;
                driver.navigate().to(targetUrl);
                currentUrl = driver.getCurrentUrl();
                log.info("Navigated to target path: {}", currentUrl);
            }

            // Set window size for screenshot
            this.setWindowSize(driver);

            // Wait for page to load completely
            Thread.sleep(tapdataConfig.getScreenshotDelayMs());

            // Take screenshot
            file = webDriverService.takeSnapShot(driver);
            log.info("Tapdata command executed successfully. URL: {}", currentUrl);
            
            endTime = System.currentTimeMillis();
            elapsedTime = (double) ((endTime - startTime)) / 1000;

        } catch (Exception e) {
            log.error("Error executing Tapdata command: ", e);
            throw e;
        } finally {
            if (driver != null) {
                driver.quit();
            }
        }
        
        return CommandResult.builder()
                .file(file)
                .command(currentUrl)
                .elapsedTime(elapsedTime)
                .build();
    }

    public CommandResult captureDashboard() throws Exception {
        log.info("captureDashboard started");
        return executeTapdataCommand("");
    }

    public CommandResult captureSpecificPage(String path) throws Exception {
        log.info("captureSpecificPage started with path: {}", path);
        return executeTapdataCommand(path);
    }

    @Override
    public CommandResult execByCommand(Command command) throws Exception {
        String commandName = command.getCommand();
        log.info("Executing Tapdata command: {}", commandName);

        switch (commandName) {
            case "captureDashboard":
                return this.captureDashboard();
            case "captureSpecificPage":
                String path = command.getStoreVariables() != null ?
                    command.getStoreVariables().get("path") : "";
                return this.captureSpecificPage(path);
            default:
                log.warn("Unknown Tapdata command: {}", commandName);
                return CommandResult.builder()
                    .rc(-1)
                    .stderr("Unknown command: " + commandName)
                    .build();
        }
    }

    @Override
    public CmdTypeEnum getCmdType() throws Exception {
        return CmdTypeEnum.TAPDATA;
    }
}
