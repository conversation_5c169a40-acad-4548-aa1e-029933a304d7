# **SC3 Chatops Manual (for internal use only)**

## Table of Contents

1. [Introduction](#introduction)
2. [Chatops useful link](#chatops-useful-link)
3. [Access HA Chat (DEV)](#ha-chat-dev)
4. [How to start chatops](#how-to-start)
5. [Available Buttons](#buttons)
6. [Available Health Check Module](#available-module)
7. [Output sample](#output)

## Introduction

The project aims to provide incident health check through HA Chat for SC3 members.

Currently, the chatops supports sybase, oracle and repserver health check.

## Start / Shutdown chatops in Linux environment

### start_chatops.sh

```
Usage: ./start_chatops.sh {dev|sit|prd} {fg|bg} {java_path}
** fg means foreground process. bg means background process.
```

e.g. `start_chatops.sh dev bg /dbms/oracle/r1220/cln_01/jdk/jre/bin/java`

### kill_chatops_bg_process.sh

```
Usage: ./kill_chatops_bg_process.sh {dev|sit|prd}
** fg means foreground process. bg means background process.
```

### auto_start_chatops.sh

Usage: ./auto_start_chatops.sh {dev|sit|prd} {java_path}
Script for crontab job to auto start the chatops if it is down.

Email will be send out every time it is being triggered.
If it failed to start the chatops after 3 consecutive retry, it will be stopped.

e.g. `*/5 * * * * /dbms/maintain/home/<USER>/chatops/auto_start_chatops.sh dev /dbms/oracle/r1220/cln_01/jdk/jre/bin/java`

## Chatops useful link <a name="chatops-useful-link"></a>

| Environment        | URL                                                        |
| ------------------ | :----------------------------------------------------------|
| DEV web portal     | https://hachatwebtesting.serverdev.hadev.org.hk/login      |
| SIT web portal     | https://hachatwebsit.server.ha.org.hk/login                |
| PROD web portal    | https://hachatweb.server.ha.org.hk/login                   |
| SC4 chatops manual | https://hagithub.home/pages/EAP/chatops/docs/chatops/cover |

## Access HA Chat (DEV) <a name="ha-chat-dev"></a>

### Web version

1. Login remote machine: wdevtsv05a
2. Access HA Chat (DEV) website https://hachatwebtesting.serverdev.hadev.org.hk/chatroom
3. Login with CORPDEV account.
4. Start itbot in the “ChatOps_Dev(SC3)” chatroom

### Mobile app version

1. in remote machine: wdevtsv05a
2. Access HA Chat (DEV) registration website. Follow the instruction to install the mobile app.
   https://hachatwebtesting.serverdev.hadev.org.hk/home/<USER>
3. Start itbot in the “ChatOps_Dev(SC3)” chatroom

## How to start chatops <a name="how-to-start"></a>

Enter `@itbot` to initialize the chatops.
Follow the instructions and select the appropriate.
![Alt text](image/init_chatops.png)

## Available Buttons <a name="buttons"></a>

| Database  | Button              | Usage                                                         |
| --------- | ------------------- | ------------------------------------------------------------- |
| Sybase    | ASE_IHC             | Incident health check script                                  |
| Oracle    | CIMS_HEALTH_CHECK   | Health check for CIMS project only                            |
| Oracle    | DB_ENABLE           | Check if db/listeners are enabled in clusterware              |
| Oracle    | HAORA               | Basic check of all db in the host and clusterware             |
| Oracle    | ORACRS              | Basic check of clusterware                                    |
| Oracle    | ORAAERRALERT        | Filter ORA errors in alert log                                |
| Oracle    | ORAINFO             | List IP related information                                   |
| Oracle    | ORAROLE             | List db up/down and database role                             |
| Oracle    | ORASERVICE          | List all db services in host                                  |
| Repserver | DBSUB_DROP          | Drop rep subscription                                         |
| Repserver | DBSUB_Repdbs_MDROP  | Drop rep def                                                  |
| Repserver | FIND_DBSUB          | Find rep subscription                                         |
| Repserver | FIND_DBSUB_REPDBS   | Find rep def                                                  |
| Repserver | REP_HC              | Health check through chk_que.sh, chk_down.sh and chk_dbsub.sh |
| Repserver | RS_STARTUP          | Restart repserver in normal mode                              |
| Repserver | RS_STARTUP_STDALONE | Restart repserver in standalone mode                          |
| Repserver | SQM_PURGE           | Purge stable queue                                            |
| Repserver | SQM_SHOW            | Show stable queue content                                     |

## Available Health Check Module <a name="available-module"></a>

All health check modules implement the ICommand interface.

| Module Name       | Description                                                        |
| ----------------- | ------------------------------------------------------------------ |
| AnsibleService    | Executes playbooks using the Ansible Automation Platform API.      |
| ClouderaService   | Interacts with the Cloudera Manager API for health checks and operations (e.g., start, stop, restart). |
| GranfanaService   | Captures dashboards from SC3 Grafana.                              |
| SshModule         | Executes shell commands via SSH.                                   |
| TapdataService    | Captures dashboards from Tapdata management web interface.         |

## Output sample <a name="output"></a>

### PDF normal result

The result text box is the stdout of the script command.
![Alt text](image/normal.png)

### PDF with script exit with return code > 0

In case of the script exit with return code > 0, the exit code and stderr will be printed in the red text box.

Stdout will still be printed in the white text box.

![Alt text](image/error.png)

### Wildcast search result > 30 item

The chatops support free text input for searching hosts/instances/projects.

- When the search result is less than 30 items, the chatops will convert the items to buttons for user to interact with.
  ![Alt text](image/list30-.png)

- When the search result is greater than 30 items, the chatops will convert the items to pdf.
  User will need to further search the target in the pdf, and then restart the chatops session.
  ![Alt text](image/list30+.png)

### Java program exception

Chatops will reply a message with the exception message.
![Alt text](image/java-exception.png)
