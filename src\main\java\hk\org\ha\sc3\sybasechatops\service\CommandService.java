package hk.org.ha.sc3.sybasechatops.service;

import java.util.List;

import org.springframework.stereotype.Service;

import hk.org.ha.sc3.sybasechatops.interfaces.ICommand;
import hk.org.ha.sc3.sybasechatops.model.db.Command;
import hk.org.ha.sc3.sybasechatops.model.db.CommandResult;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public abstract class CommandService {

    private SshService sshService;
    private GrafanaService grafanaService;
    private TapdataService tapdataService;

    public ICommand getCommandExecutor(Command command, String hostName, String instanceName) {

        switch (command.getCmdType()) {
            case SSH:
                return this.sshService;
            case GRAFANA:
                return this.grafanaService;
            case TAPDATA:
                return this.tapdataService;
            default:
                return this.sshService;
        }
    }

    public abstract List<CommandResult> exec(String hostName, String instanceName,
            String command)
            throws Exception;
}
