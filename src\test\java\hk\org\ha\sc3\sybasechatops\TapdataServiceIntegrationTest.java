package hk.org.ha.sc3.sybasechatops;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import hk.org.ha.sc3.sybasechatops.service.TapdataService;

@SpringBootTest
@TestPropertySource(locations = "classpath:application.yml")
class TapdataServiceIntegrationTest {

    @Autowired
    private TapdataService tapdataService;

    @Test
    void testCaptureDashboard() throws Exception {
        this.tapdataService.captureDashboard();
    }
}
