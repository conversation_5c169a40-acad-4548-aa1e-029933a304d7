-- Description: Add support for Tapdata dashboard capture commands in ChatOps

-- Add Tapdata commands
INSERT INTO `health_check`.`chatops_command` (`id`, `command`, `enabled`, `user`, `timeout`, `cmd_type`, `return_type`) VALUES
('TAPDATA_CAPTURE_DASHBOARD', 'captureDashboard', 1, NULL, 120, 'TAPDATA', 'IMAGE'),
('TAPDATA_CAPTURE_SPECIFIC_PAGE', 'captureSpecificPage', 1, NULL, 120, 'TAPDATA', 'IMAGE');

-- Add Tapdata command groups
INSERT INTO `health_check`.`chatops_command_group` (`id`, `cmd_type`, `db_type`, `enabled`) VALUES
('TAPDATA_DASHBOARD_CAPTURE', 'INSTANCE', 'TAPDATA', 1),
('TAPDATA_PAGE_CAPTURE', 'INSTANCE', 'TAPDA<PERSON>', 1);

-- Add command group mappings
INSERT INTO `health_check`.`chatops_command_group_mapping` (`id`, `command_id`, `command_group_id`, `pdf_title`, `wait_for_id`, `exec_mode`, `sequence`) VALUES
(1001, 'TAPDATA_CAPTURE_DASHBOARD', 'TAPDATA_DASHBOARD_CAPTURE', 'Tapdata Dashboard Capture', NULL, NULL, 1),
(1002, 'TAPDATA_CAPTURE_SPECIFIC_PAGE', 'TAPDATA_PAGE_CAPTURE', 'Tapdata Page Capture', NULL, NULL, 1);

-- Add Tapdata to global allowed database types
INSERT INTO `health_check`.`chatops_global_allowed_db` (`database_type`) VALUES ('TAPDATA');

-- Add a sample Tapdata instance to the non-db product table (if needed)
INSERT INTO `health_check`.`chatops_non_db_product` (`type`, `host`, `instance`, `team`) VALUES
('TAPDATA', 'tapcdcvmctst71a', 'tapdata-mgmt', 'SC3');

-- //@UNDO
-- Remove command group mappings
DELETE FROM `health_check`.`chatops_command_group_mapping` WHERE `id` IN (1001, 1002);

-- Remove command groups
DELETE FROM `health_check`.`chatops_command_group` WHERE `id` IN ('TAPDATA_DASHBOARD_CAPTURE', 'TAPDATA_PAGE_CAPTURE');

-- Remove commands
DELETE FROM `health_check`.`chatops_command` WHERE `id` IN ('TAPDATA_CAPTURE_DASHBOARD', 'TAPDATA_CAPTURE_SPECIFIC_PAGE');

-- Remove from global allowed database types
DELETE FROM `health_check`.`chatops_global_allowed_db` WHERE `database_type` = 'TAPDATA';

-- Remove sample instance
DELETE FROM `health_check`.`chatops_non_db_product` WHERE `type` = 'TAPDATA' AND `host` = 'tapcdcvmctst71a' AND `instance` = 'tapdata-mgmt';
