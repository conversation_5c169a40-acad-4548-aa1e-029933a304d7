-- Description: Add support for Tapdata dashboard capture commands in ChatOps

-- Add Tapdata commands
INSERT INTO `health_check`.`chatops_command` (`id`, `command`, `enabled`, `user`, `timeout`, `cmd_type`, `return_type`) VALUES
('TAPDATA_CAPTURE_PAGE', 'capturePage', 1, NULL, 120, 'TAPDATA', 'IMAGE');

-- Add Tapdata command groups
INSERT INTO `health_check`.`chatops_command_group` (`id`, `cmd_type`, `db_type`, `enabled`) VALUES
('TAPDATA_PAGE_CAPTURE', 'INSTANCE', 'TAPDATA', 1);

-- Add command arguments
INSERT INTO `health_check`.`chatops_command_argument` (`id`, `key_name`, `description`, `required_para`, `arg_type`) VALUES
('ARG_TAPDATA_PAGE', 'page', 'Select Tapdata page to capture', NULL, 'ARG_TAPDATA_PAGE');

-- Add command group mappings
INSERT INTO `health_check`.`chatops_command_group_mapping` (`id`, `command_id`, `command_group_id`, `pdf_title`, `wait_for_id`, `exec_mode`, `sequence`) VALUES
(1001, 'TAPDATA_CAPTURE_PAGE', 'TAPDATA_PAGE_CAPTURE', 'Tapdata Page Capture', NULL, NULL, 1);

-- Add command argument mappings
INSERT INTO `health_check`.`chatops_cmd_arg_mapping` (`id`, `command_id`, `command_argument_id`, `sequence`) VALUES
(1001, 'TAPDATA_CAPTURE_PAGE', 'ARG_TAPDATA_PAGE', 1);

-- Add Tapdata to global allowed database types
INSERT INTO `health_check`.`chatops_global_allowed_db` (`database_type`) VALUES ('TAPDATA');

-- Add a sample Tapdata instance to the non-db product table (if needed)
INSERT INTO `health_check`.`chatops_non_db_product` (`type`, `host`, `instance`, `team`) VALUES
('TAPDATA', 'tapcdcvmctst71a', 'tapdata-mgmt', 'SC3');

-- //@UNDO
-- Remove command argument mappings
DELETE FROM `health_check`.`chatops_cmd_arg_mapping` WHERE `id` = 1001;

-- Remove command group mappings
DELETE FROM `health_check`.`chatops_command_group_mapping` WHERE `id` = 1001;

-- Remove command arguments
DELETE FROM `health_check`.`chatops_command_argument` WHERE `id` = 'ARG_TAPDATA_PAGE';

-- Remove command groups
DELETE FROM `health_check`.`chatops_command_group` WHERE `id` = 'TAPDATA_PAGE_CAPTURE';

-- Remove commands
DELETE FROM `health_check`.`chatops_command` WHERE `id` = 'TAPDATA_CAPTURE_PAGE';

-- Remove from global allowed database types
DELETE FROM `health_check`.`chatops_global_allowed_db` WHERE `database_type` = 'TAPDATA';

-- Remove sample instance
DELETE FROM `health_check`.`chatops_non_db_product` WHERE `type` = 'TAPDATA' AND `host` = 'tapcdcvmctst71a' AND `instance` = 'tapdata-mgmt';
